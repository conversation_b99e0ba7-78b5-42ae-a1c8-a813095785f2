# 基于官方的PHP-FPM镜像
FROM php:8.2-fpm-alpine

# 设置工作目录
WORKDIR /var/www/likeadmin

# --- 权限修复核心 ---
# 定义一个参数，用于接收主机的用户组ID (GID)，默认为1000
ARG DOCKER_GID=1000

# 安装shadow包，它提供了groupmod等用户组管理工具
# 并且在同一层执行所有权限操作
RUN apk add --no-cache shadow \
    && echo "--- 正在调整用户组权限 ---" \
    # 检查目标GID是否存在，不存在则创建一个名为'dockergroup'的组
    && if ! getent group ${DOCKER_GID} >/dev/null; then \
           groupadd -g ${DOCKER_GID} dockergroup; \
       else \
           echo "Group with GID ${DOCKER_GID} already exists."; \
       fi \
    # 获取目标GID的组名
    && DOCKER_GROUP_NAME=$(getent group ${DOCKER_GID} | cut -d: -f1) \
    # 将www-data用户(UID 82)加入到这个组中
    && usermod -a -G ${DOCKER_GROUP_NAME} www-data

# --- 依赖安装 (保持不变) ---
RUN apk add --no-cache \
        $PHPIZE_DEPS \
        libzip-dev libpng-dev libjpeg-turbo-dev freetype-dev \
        oniguruma-dev libxml2-dev linux-headers

RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        fileinfo pdo_mysql gd dom bcmath sockets pcntl zip \
    && pecl install redis && docker-php-ext-enable redis

COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

CMD ["php-fpm"]