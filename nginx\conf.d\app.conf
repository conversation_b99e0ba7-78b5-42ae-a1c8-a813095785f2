# --- Server Block for HTTPS ---
# 处理所有HTTPS流量的主配置
server {
    listen 80;
    listen 443 ssl http2; # 启用SSL和HTTP/2以获得更好性能
    server_name *.keeymedia.cn; # 您的域名

    # --- SSL 配置 ---
    # 证书和私钥路径指向我们在docker-compose中挂载的目录
    ssl_certificate /etc/nginx/ssl/keeymedia.cn.fullchain.cer; # <-- 修改为您实际的证书文件名
    ssl_certificate_key /etc/nginx/ssl/keeymedia.cn.key; # <-- 修改为您实际的私钥文件名

    # --- 安全性与性能优化 ---
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers "EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH";
    ssl_ecdh_curve secp384r1;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    # resolver ******* ******* valid=300s; # OCSP Stapling需要DNS解析器
    # resolver_timeout 5s;
    # add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"; # HSTS头部
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    
    # 日志和请求体大小限制
    access_log /var/log/nginx/likeadmin.localhost_access.log;
    error_log /var/log/nginx/likeadmin.localhost_error.log;
    client_max_body_size 1000M;

    # 项目Web入口目录
    root /var/www/likeadmin/server/public;
    index index.php index.html index.htm;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        if (!-e $request_filename)
        {
            rewrite ^/(.*)$ /index.php?s=$1 last;
            break;
        }
    }
    location ~ /.*\.php/ {
        rewrite ^(.*?/?)(.*\.php)(.*)$ /$2?s=$3 last;
        break;
    }

    location ~ \.php$ {
        fastcgi_pass   php:9000;
        fastcgi_index  index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include        fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}


# # --- Server Block for HTTP ONLY ---
# # 仅监听80端口，用于调试
# server {
#     listen 80;
#     server_name *.keeymedia.cn; # 您的域名

#     # 日志和请求体大小限制
#     access_log /var/log/nginx/likeadmin.localhost_access.log;
#     error_log /var/log/nginx/likeadmin.localhost_error.log;
#     client_max_body_size 5M;

#     # 项目Web入口目录
#     root /var/www/likeadmin/server/public;
#     index index.php index.html index.htm;

#     # 伪静态规则 (与之前HTTPS块中的完全相同)
#     location / {
#         try_files $uri $uri/ /index.php?$query_string;
#     }

#     location ~ \.php$ {
#         fastcgi_pass   php:9000;
#         fastcgi_index  index.php;
#         fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
#         include        fastcgi_params;
#     }

#     location ~ /\.ht {
#         deny all;
#     }
# }


# server {
#     listen 80;
#     listen 443 ssl http2; # 启用SSL和HTTP/2以获得更好性能
#     server_name *.keeymedia.cn; # 您的域名

#     # --- SSL 配置 ---
#     # 证书和私钥路径指向我们在docker-compose中挂载的目录
#     ssl_certificate /etc/nginx/ssl/keeymedia.cn.fullchain.cer; # <-- 修改为您实际的证书文件名
#     ssl_certificate_key /etc/nginx/ssl/keeymedia.cn.key; # <-- 修改为您实际的私钥文件名
#     access_log /logs/demo.likeadmin.cnt_access_nginx.log;
#     error_log /logs/demo.likeadmin.cn_error_nginx.log;
#     client_max_body_size 5M;
#     location / {
#         root  likeadmin/server/public;#入口文件目录
#         index  index.html index.htm index.php;
#         if (!-e $request_filename)
#         {
#             rewrite ^/(.*)$ /index.php?s=$1 last;
#             break;
#         }
#     }
#     location ~ /.*\.php/ {
#         rewrite ^(.*?/?)(.*\.php)(.*)$ /$2?s=$3 last;
#         break;
#     }
#     error_page   500 502 503 504  /50x.html;
#     location = /50x.html {
#         root   /var/www/html;
#     }

#     location ~ \.php$ {
#         fastcgi_pass   php:9000;
#         fastcgi_index  index.php;
#         fastcgi_param  SCRIPT_FILENAME  likeadmin/server/public$fastcgi_script_name; #入口文件目录
#         include        fastcgi_params;
#     }
#     location = /favicon.ico {
#             log_not_found off;
#             access_log off;
#         }
# }