{"private": true, "scripts": {"build": "nuxt generate && node scripts/build.mjs", "build:ssr": "nuxt build && node scripts/build.mjs", "dev": "nuxt dev", "start": "nuxt start", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@element-plus/nuxt": "1.0.5", "@nuxt/webpack-builder": "3.10.0", "@nuxtjs/tailwindcss": "6.8.0", "@pinia/nuxt": "0.4.11", "@vue/eslint-config-prettier": "9.0.0", "@vue/eslint-config-typescript": "12.0.0", "eslint": "8.56.0", "eslint-plugin-nuxt": "4.0.0", "eslint-plugin-vue": "9.18.1", "eslint-plugin-import": "2.27.5", "nuxt": "3.6.5", "nuxt-lodash": "2.5.0", "prettier": "3.2.4", "vue": "3.3.4", "sass": "1.62.1", "sass-loader": "14.1.0", "typescript": "4.9.3", "vite-plugin-svg-icons": "2.0.1"}, "dependencies": {"@chenfengyuan/vue-countdown": "2.1.1", "element-plus": "2.5.2", "pinia": "2.0.3", "vue-cropper": "1.0.5"}}